import React, { useState, useEffect, useCallback } from 'react';
import {
  calcularMassaMolar,
  parseComposto,
  formatChemicalFormula,
  formatScientificNotation,
  canDisplace,
  commonAcids,
  commonBases,
} from '../constants/chemistryUtils';
import {
  diatomicElements,
  CommonIonsData
} from '../constants/chemistryConstants';
import Select from 'react-select';


const commonAnions = CommonIonsData.commonAnions
const commonCations = CommonIonsData.commonCations

// Utilitário: extrai símbolo e carga de um íon (ex: 'Ca²⁺' → {symbol: 'Ca', charge: 2})
function parseIonSymbol(ionSymbol) {
  // Remove subscritos unicode e converte para número
  const match = ionSymbol.match(/^([A-Za-z0-9₀₁₂₃₄₅₆₇₈₉]+)([⁺⁻+\-\d]*)$/);
  if (!match) return { symbol: ionSymbol, charge: 0 };
  const symbol = match[1].replace(/[₀₁₂₃₄₅₆₇₈₉]/g, d => '0123456789'['₀₁₂₃₄₅₆₇₈₉'.indexOf(d)]);
  let charge = 0;
  // Suporta formatos: ²⁺, ³⁻, +, -, 2+, 3-, etc
  const chargeStr = match[2];
  if (chargeStr) {
    // Unicode sobrescrito
    const unicodeMap = { '⁺': '+', '⁻': '-', '²': '2', '³': '3', '⁴': '4', '⁵': '5', '⁶': '6', '⁷': '7', '⁸': '8', '⁹': '9', '¹': '1', '⁰': '0' };
    let norm = chargeStr.replace(/[⁺⁻²³⁴⁵⁶⁷⁸⁹¹⁰]/g, c => unicodeMap[c] || c);
    // Ex: 2+, 3-, +, -
    const m = norm.match(/([0-9]*)([+-])/);
    if (m) {
      charge = parseInt(m[1] || '1') * (m[2] === '+' ? 1 : -1);
    }
  }
  return { symbol, charge };
}

// Utilitário: verifica se uma equação está balanceada em termos de cargas
function isChargeBalanced(reactants, products) {
  try {
    const { commonCations, commonAnions } = CommonIonsData;

    // Função para calcular carga total de um composto
    const calculateTotalCharge = (compounds) => {
      let totalCharge = 0;

      compounds.forEach(compound => {
        const formula = compound.formula;
        const coefficient = compound.coefficient || 1;

        // Buscar íons conhecidos na fórmula
        let compoundCharge = 0;

        // Verificar cátions
        for (const cation of commonCations) {
          const ionInfo = parseIonSymbol(cation.symbol);
          if (formula.includes(ionInfo.symbol)) {
            compoundCharge += ionInfo.charge;
          }
        }

        // Verificar ânions
        for (const anion of commonAnions) {
          const ionInfo = parseIonSymbol(anion.symbol);
          if (formula.includes(ionInfo.symbol)) {
            compoundCharge += ionInfo.charge;
          }
        }

        totalCharge += compoundCharge * coefficient;
      });

      return totalCharge;
    };

    const reactantCharge = calculateTotalCharge(reactants);
    const productCharge = calculateTotalCharge(products);

    return Math.abs(reactantCharge - productCharge) < 0.001; // Tolerância para erros de ponto flutuante
  } catch (error) {
    console.warn('Erro ao verificar balanceamento de cargas:', error);
    return true; // Se não conseguir verificar, assume que está balanceado
  }
}

// Utilitário: monta fórmula iônica balanceada a partir de cátion e ânion
function buildIonicFormula(cation, anion) {
  // Ex: Ca²⁺ + Cl⁻ → CaCl2
  const cationInfo = parseIonSymbol(cation.symbol || cation);
  const anionInfo = parseIonSymbol(anion.symbol || anion);
  const c = Math.abs(cationInfo.charge);
  const a = Math.abs(anionInfo.charge);
  if (!cationInfo.charge || !anionInfo.charge) {
    // fallback: só concatena
    return cationInfo.symbol + anionInfo.symbol;
  }
  // Mínimo múltiplo comum
  const mmc = (x, y) => !y ? x : mmc(y, x % y);
  const lcm = (c && a) ? (c * a) / mmc(c, a) : 1;
  const nCation = lcm / c;
  const nAnion = lcm / a;
  let formula = '';
  formula += cationInfo.symbol + (nCation > 1 ? nCation : '');
  formula += anionInfo.symbol + (nAnion > 1 ? nAnion : '');
  return formula;
}

function enforceDiatomic(formula) {
  const match = formula.match(/^([A-Z][a-z]?)(\d*)$/);
  if (match) {
    const symbol = match[1];
    const subscript = match[2];
    if (diatomicElements.includes(symbol)) {
      if (subscript === '' || subscript === '1') {
        return symbol + '2';
      }
    }
  }
  return formula;
}

// Custom styles for selects
const selectStyles = {
  control: (provided) => ({
    ...provided,
    backgroundColor: 'white',
    borderColor: 'var(--border-color)',
    minHeight: '30px',
    height: '30px',
    boxShadow: 'none',
  }),
  valueContainer: (provided) => ({
    ...provided,
    height: '30px',
    padding: '0 6px',
  }),
  input: (provided) => ({
    ...provided,
    margin: '0px',
  }),
  indicatorsContainer: (provided) => ({
    ...provided,
    height: '30px',
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected ? 'var(--primary-color)' :
                    state.isFocused ? 'rgba(76, 175, 80, 0.2)' : 'white',
    color: state.isSelected ? 'white' : 'black',
  }),
};

// Styles for internal tabs
const tabStyles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    marginTop: '20px',
  },
  tabList: {
    display: 'flex',
    borderBottom: '1px solid var(--border-color)',
    marginBottom: '15px',
  },
  tab: {
    padding: '8px 16px',
    cursor: 'pointer',
    backgroundColor: 'transparent',
    border: 'none',
    borderBottom: '2px solid transparent',
    transition: 'all 0.3s ease',
    color: '#555',
  },
  activeTab: {
    borderBottom: '2px solid var(--primary-color)',
    color: 'var(--primary-color)',
    fontWeight: 'bold',
  },
  tabContent: {
    padding: '10px 0',
  },
};

// Function to parse a chemical equation
const parseEquation = (equation) => {
  if (!equation) return { reactants: [], products: [] };

  // Split the equation into reactants and products
  const sides = equation.split('->');
  if (sides.length !== 2) {
    return { error: 'Invalid format. Use -> to separate reactants and products.' };
  }

  const reactantsStr = sides[0].trim();
  const productsStr = sides[1].trim();

  // Parse reactants
  const reactants = reactantsStr.split('+').map(compound => {
    compound = compound.trim();
    // Check if there's a coefficient
    const coeffMatch = compound.match(/^(\d+)(.+)$/);
    if (coeffMatch) {
      return {
        coefficient: parseInt(coeffMatch[1], 10),
        formula: coeffMatch[2].trim()
      };
    }
    return {
      coefficient: 1,
      formula: compound
    };
  });

  // Parse products
  const products = productsStr.split('+').map(compound => {
    compound = compound.trim();
    // Check if there's a coefficient
    const coeffMatch = compound.match(/^(\d+)(.+)$/);
    if (coeffMatch) {
      return {
        coefficient: parseInt(coeffMatch[1], 10),
        formula: coeffMatch[2].trim()
      };
    }
    return {
      coefficient: 1,
      formula: compound
    };
  });

  return { reactants, products };
};

// Function to balance a chemical equation using matrix algebra
const balanceEquation = (reactants, products) => {
  try {
    // Get all unique elements
    const allElements = new Set();
    const allCompounds = [...reactants, ...products];

    allCompounds.forEach(compound => {
      const parseResult = parseComposto(compound.formula);
      const elements = parseResult.elementos || parseResult;
      elements.forEach(el => allElements.add(el.simbolo));
    });

    const elementsList = Array.from(allElements);
    const numCompounds = allCompounds.length;

    // Create matrix where each row is an element and each column is a compound
    const matrix = [];

    elementsList.forEach(element => {
      const row = [];
      allCompounds.forEach((compound, index) => {
        const parseResult = parseComposto(compound.formula);
        const elements = parseResult.elementos || parseResult;
        const elementData = elements.find(el => el.simbolo === element);
        const count = elementData ? elementData.quantidade : 0;
        
        // For products, use negative values (moved to left side of equation)
        if (index >= reactants.length) {
          row.push(-count);
        } else {
          row.push(count);
        }
      });
      matrix.push(row);
    });

    // Try to solve using simple integer solutions
    const coefficients = solveBalanceMatrix(matrix, numCompounds);

    if (coefficients) {
      // Apply coefficients to compounds
      const balancedReactants = reactants.map((reactant, index) => ({
        ...reactant,
        coefficient: coefficients[index]
      }));

      const balancedProducts = products.map((product, index) => ({
        ...product,
        coefficient: coefficients[reactants.length + index]
      }));

      // Verify the balance
      const elementCount = {};

      // Count elements in balanced reactants
      balancedReactants.forEach(reactant => {
        const parseResult = parseComposto(reactant.formula);
        const elements = parseResult.elementos || parseResult;
        elements.forEach(el => {
          const key = el.simbolo;
          if (!elementCount[key]) elementCount[key] = { reactants: 0, products: 0 };
          elementCount[key].reactants += el.quantidade * reactant.coefficient;
        });
      });

      // Count elements in balanced products
      balancedProducts.forEach(product => {
        const parseResult = parseComposto(product.formula);
        const elements = parseResult.elementos || parseResult;
        elements.forEach(el => {
          const key = el.simbolo;
          if (!elementCount[key]) elementCount[key] = { reactants: 0, products: 0 };
          elementCount[key].products += el.quantidade * product.coefficient;
        });
      });

      const isElementBalanced = Object.values(elementCount).every(
        count => count.reactants === count.products
      );

      // Verificar também o balanceamento de cargas para reações iônicas
      const isChargeBalancedResult = isChargeBalanced(balancedReactants, balancedProducts);

      const isBalanced = isElementBalanced && isChargeBalancedResult;

      return {
        isBalanced,
        elementCount,
        balancedReactants,
        balancedProducts,
        chargeBalanced: isChargeBalancedResult
      };
    }

    // If automatic balancing fails, return original with balance check
    const elementCount = {};

    reactants.forEach(reactant => {
      const parseResult = parseComposto(reactant.formula);
      const elements = parseResult.elementos || parseResult;
      elements.forEach(el => {
        const key = el.simbolo;
        if (!elementCount[key]) elementCount[key] = { reactants: 0, products: 0 };
        elementCount[key].reactants += el.quantidade * reactant.coefficient;
      });
    });

    products.forEach(product => {
      const parseResult = parseComposto(product.formula);
      const elements = parseResult.elementos || parseResult;
      elements.forEach(el => {
        const key = el.simbolo;
        if (!elementCount[key]) elementCount[key] = { reactants: 0, products: 0 };
        elementCount[key].products += el.quantidade * product.coefficient;
      });
    });

    const isElementBalanced = Object.values(elementCount).every(
      count => count.reactants === count.products
    );

    // Verificar também o balanceamento de cargas
    const isChargeBalancedResult = isChargeBalanced(reactants, products);

    const isBalanced = isElementBalanced && isChargeBalancedResult;

    return {
      isBalanced,
      elementCount,
      balancedReactants: [...reactants],
      balancedProducts: [...products],
      chargeBalanced: isChargeBalancedResult
    };

  } catch (error) {
    console.error('Error balancing equation:', error);
    return {
      isBalanced: false,
      elementCount: {},
      balancedReactants: [...reactants],
      balancedProducts: [...products]
    };
  }
};

// Helper function to solve the balance matrix
const solveBalanceMatrix = (matrix, numCompounds) => {
  // Try to solve systematically for common equations

  // Special cases for common equation patterns
  if (numCompounds === 3) {
    // A + B -> C pattern
    return trySimpleBalance(matrix, numCompounds);
  } else if (numCompounds === 4) {
    // A + B -> C + D pattern
    return trySimpleBalance(matrix, numCompounds);
  }

  // For more complex equations, use brute force with optimized search
  return tryBruteForceBalance(matrix, numCompounds);
};

// Try simple balancing for common patterns
const trySimpleBalance = (matrix, numCompounds) => {
  const maxCoeff = 30; // Aumenta o limite para permitir balanceamento de reações como Na + HCl

  // Try systematic approach starting with coefficient 1 for first compound
  for (let c1 = 1; c1 <= maxCoeff; c1++) {
    for (let c2 = 1; c2 <= maxCoeff; c2++) {
      if (numCompounds === 3) {
        for (let c3 = 1; c3 <= maxCoeff; c3++) {
          const coeffs = [c1, c2, c3];
          if (testCoefficients(matrix, coeffs)) {
            return reduceCoefficients(coeffs);
          }
        }
      } else if (numCompounds === 4) {
        for (let c3 = 1; c3 <= maxCoeff; c3++) {
          for (let c4 = 1; c4 <= maxCoeff; c4++) {
            const coeffs = [c1, c2, c3, c4];
            if (testCoefficients(matrix, coeffs)) {
              return reduceCoefficients(coeffs);
            }
          }
        }
      }
    }
  }

  return null;
};

// Try brute force for complex equations
const tryBruteForceBalance = (matrix, numCompounds) => {

  // Aumenta o limite para permitir balanceamento de reações como C2H6 + O2
  // e outras combustões que precisam de coeficientes maiores
  const maxCoeff = 12;

  // Para evitar explosão combinatória, priorize balanceamento de combustão
  // (hidrocarboneto + O2 -> CO2 + H2O) com abordagem otimizada
  if (numCompounds === 4) {
    // Tenta balancear CxHy + O2 -> CO2 + H2O
    // Assume ordem: [hidrocarboneto, O2, CO2, H2O]
    // Só aplica se realmente for combustão
    const [rowC, rowH, rowO] = matrix;
    if (rowC && rowH && rowO && rowC[0] > 0 && rowC[2] < 0 && rowH[0] > 0 && rowH[3] < 0 && rowO[1] > 0) {
      // CxHy + O2 -> CO2 + H2O
      // coeficientes: a CxHy + b O2 -> c CO2 + d H2O
      // C: x*a = c
      // H: y*a = 2*d
      // O: 2*b = 2*c + d
      for (let a = 1; a <= maxCoeff; a++) {
        const x = rowC[0];
        const y = rowH[0];
        const c = x * a;
        const d = (y * a) / 2;
        const b = (2 * c + d) / 2;
        // Se algum coeficiente não for inteiro, multiplica todos por 2 (ou 4) para tentar obter inteiros
        let mult = 1;
        if (!Number.isInteger(d) || !Number.isInteger(b)) {
          mult = 2;
          if (!Number.isInteger(d * 2) || !Number.isInteger(b * 2)) {
            mult = 4;
          }
        }
        const a2 = a * mult;
        const b2 = b * mult;
        const c2 = c * mult;
        const d2 = d * mult;
        // Só aceita se todos positivos e inteiros
        if ([a2, b2, c2, d2].every(v => Number.isInteger(v) && v > 0)) {
          const coeffs = [a2, b2, c2, d2];
          if (testCoefficients(matrix, coeffs)) {
            return reduceCoefficients(coeffs);
          }
        }
      }
    }
  }

  // Se não for combustão, faz brute force tradicional
  const testCombination = (coeffs) => {
    return testCoefficients(matrix, coeffs);
  };

  const findSolution = (current, remaining) => {
    if (remaining === 0) {
      if (current.every(c => c > 0) && testCombination(current)) {
        return reduceCoefficients([...current]);
      }
      return null;
    }
    for (let i = 1; i <= maxCoeff; i++) {
      current.push(i);
      const result = findSolution(current, remaining - 1);
      if (result) {
        return result;
      }
      current.pop();
    }
    return null;
  };
  return findSolution([], numCompounds);
};

// Test if coefficients satisfy the balance equations
const testCoefficients = (matrix, coeffs) => {
  for (const row of matrix) {
    let sum = 0;
    for (let i = 0; i < row.length && i < coeffs.length; i++) {
      sum += row[i] * coeffs[i];
    }
    if (Math.abs(sum) > 0.001) {
      return false;
    }
  }
  return true;
};

// Reduce coefficients to smallest integers
const reduceCoefficients = (coeffs) => {
  const gcd = (a, b) => b === 0 ? Math.abs(a) : gcd(b, a % b);
  const gcdAll = coeffs.reduce((acc, val) => gcd(acc, val));

  if (gcdAll > 1) {
    return coeffs.map(c => c / gcdAll);
  }
  return coeffs;
};


// Component to predict reaction products
const ProductPredictor = () => {
  const [reactant1, setReactant1] = useState('');
  const [reactant2, setReactant2] = useState('');
  const [predictedProducts, setPredictedProducts] = useState([]);
  const [predictError, setPredictError] = useState('');
  const [selectedReaction, setSelectedReaction] = useState(null);

  // Função para identificar o tipo de composto
  const identifyCompoundType = (formula) => {
    try {
      const parseResult = parseComposto(formula);
      // DEBUG: mostrar resultado do parseComposto
      console.log('DEBUG parseComposto:', formula, parseResult);
      // Filtrar apenas elementos com quantidade > 0
      const elements = (parseResult.elementos || parseResult).filter(el => el.quantidade > 0);
      const elementSymbols = elements.map(el => el.simbolo);

      // Sempre começa como compound
      let result = { type: 'compound', formula, elements };

      // Se for elemento simples
      if (elementSymbols.length === 1) {
        result.type = 'element';
        result.symbol = elementSymbols[0];
        console.log('DEBUG identificado como elemento:', result);
        return result;
      }

      // Normalizar fórmula para checagem de O2
      const normalizedFormula = (typeof formula === 'string') ? formula.replace(/\s/g, '').toUpperCase().replace('₂', '2') : '';

      // Refinar classificação
      if (commonAcids[formula]) {
        result.class = 'acid';
        result.info = commonAcids[formula];
        console.log('DEBUG identificado como ácido:', result);
      } else if (commonBases[formula]) {
        result.class = 'base';
        result.info = commonBases[formula];
        console.log('DEBUG identificado como base:', result);
      } else if (
        elementSymbols.includes('C') && elementSymbols.includes('H')
      ) {
        // Primeiro classifica como composto orgânico genérico
        result.class = 'organic_compound';
        // Se só tiver C e H, é hidrocarboneto
        if (elementSymbols.every(symbol => ['C', 'H'].includes(symbol))) {
          result.class = 'hydrocarbon';
          console.log('DEBUG identificado como hidrocarboneto:', result);
        }
        // Se tiver C, H e O (e só esses), é orgânico oxigenado
        else if (
          elementSymbols.includes('O') &&
          elementSymbols.every(symbol => ['C', 'H', 'O'].includes(symbol))
        ) {
          result.class = 'organic_oxygenated';
          console.log('DEBUG identificado como orgânico oxigenado:', result);
        } else {
          console.log('DEBUG identificado como composto orgânico genérico:', result);
        }
      } else if (
        normalizedFormula === 'O2' ||
        normalizedFormula === 'O₂' ||
        (elementSymbols.length === 1 && elementSymbols[0] === 'O' && elements[0].quantidade === 2)
      ) {
        result.class = 'oxygen';
        console.log('DEBUG identificado como oxigênio:', result);
      } else {
        console.log('DEBUG composto não classificado especificamente:', result);
      }

      return result;
    } catch (error) {
      // DEBUG: mostrar erro e resultado do parse
      console.log('DEBUG identifyCompoundType error:', formula, error);
      return { type: 'unknown', formula, error: error?.message };
    }
  };


  // 1. NEUTRALIZAÇÃO: Ácido + Base → Sal + H2O
  const predictNeutralizationProducts = (acid, base) => {
    try {
      // Buscar cátion e ânion nas listas de íons comuns
      const { commonCations, commonAnions } = require('../../chemistry/constants/chemistryConstants');
      // Extrair cátion da base (remover OH)
      let baseCationSymbol = base.formula.replace(/\(OH\)(\d*)|OH/g, '');
      // Extrair ânion do ácido (remover H)
      let acidAnionSymbol = acid.formula.replace(/^H(\d*)/g, '');
      // Casos especiais para ácidos comuns
      if (acid.formula === 'HCl') acidAnionSymbol = 'Cl';
      else if (acid.formula === 'HNO3') acidAnionSymbol = 'NO3';
      else if (acid.formula === 'H2SO4') acidAnionSymbol = 'SO4';
      else if (acid.formula === 'H3PO4') acidAnionSymbol = 'PO4';
      // Buscar objeto cátion
      let cation = commonCations.find(c => c.symbol.includes(baseCationSymbol)) || { symbol: baseCationSymbol, charge: 1 };
      // Buscar objeto ânion
      let anion = commonAnions.find(a => a.symbol.includes(acidAnionSymbol)) || { symbol: acidAnionSymbol, charge: -1 };
      // Montar fórmula balanceada
      const salt = buildIonicFormula(cation, anion);
      return [salt, 'H2O'];
    } catch (error) {
      return ['Salt', 'H2O']; // Fallback genérico
    }
  };

  // 2. SÍNTESE: Elemento + Elemento → Composto (apenas o mais provável)
  const predictSynthesisProducts = (element1, element2) => {
    const sym1 = element1.symbol;
    const sym2 = element2.symbol;

    // Se ambos são não-metais, retorna molécula diatômica se aplicável
    // Se for H2 + O2, retorna H2O (caso especial)
    if ((sym1 === 'H' && sym2 === 'O') || (sym1 === 'O' && sym2 === 'H')) {
      return ['H2O'];
    }
    // Tenta identificar cátion e ânion
    let cation = commonCations.find(c => c.symbol.replace(/[⁺²³⁴⁵⁶⁷⁸⁹¹⁰]/g,"").startsWith(sym1));
    let anion = commonAnions.find(a => a.symbol.replace(/[⁻²³⁴⁵⁶⁷⁸⁹¹⁰]/g,"").startsWith(sym2));
    if (!cation && !anion) {
      // Tenta inverter
      cation = commonCations.find(c => c.symbol.replace(/[⁺²³⁴⁵⁶⁷⁸⁹¹⁰]/g,"").startsWith(sym2));
      anion = commonAnions.find(a => a.symbol.replace(/[⁻²³⁴⁵⁶⁷⁸⁹¹⁰]/g,"").startsWith(sym1));
      if (cation && anion) {
        return [buildIonicFormula(cation, anion)];
      }
    } else if (cation && anion) {
      return [buildIonicFormula(cation, anion)];
    }

    // Se não encontrou íons, retorna concatenação simples
    return [`${sym1}${sym2}`];
  };

  // 3. TROCA SIMPLES: Elemento + Composto → Elemento + Composto
  // Elementos diatômicos comuns

  const predictSingleDisplacementProducts = (compound1, compound2) => {
    const element = compound1.type === 'element' ? compound1 : compound2;
    const compound = compound1.type === 'compound' ? compound1 : compound2;

    try {
      const elementSymbol = element.symbol;
      const compoundElements = compound.elements;

      // Verificar se o elemento pode deslocar algum elemento do composto
      for (const compoundElement of compoundElements) {
        if (canDisplace(elementSymbol, compoundElement.simbolo)) {
          // Formar novo composto com o elemento deslocador
          const otherElements = compoundElements.filter(el => el.simbolo !== compoundElement.simbolo);

          let newCompound = elementSymbol;
          otherElements.forEach(el => {
            newCompound += el.simbolo;
            if (el.quantidade > 1) newCompound += el.quantidade;
          });

          // Se o elemento deslocado for diatômico, retorna como molécula diatômica
          let displaced = compoundElement.simbolo;
          if (diatomicElements.includes(displaced)) {
            displaced = displaced + '2';
          }

          return [newCompound, displaced];
        }
      }

      return []; // Nenhuma reação ocorre
    } catch (error) {
      return [];
    }
  };

  // 4. TROCA DUPLA: Composto + Composto → Composto + Composto
  const predictDoubleDisplacementProducts = (compound1, compound2) => {
    try {
      // Casos específicos conhecidos
      const formula1 = compound1.formula;
      const formula2 = compound2.formula;
      const { commonCations, commonAnions } = require('../../chemistry/constants/chemistryConstants');
      // Caso genérico - tentar extrair íons
      const elements1 = compound1.elements;
      const elements2 = compound2.elements;
      if (elements1.length >= 2 && elements2.length >= 2) {
        // Assumir que o primeiro elemento é o cátion e o resto é o ânion
        const cation1Symbol = elements1[0].simbolo;
        const cation2Symbol = elements2[0].simbolo;
        // Remover escapes desnecessários: \d -> d
        let anion1Symbol = formula1.replace(new RegExp(`^${cation1Symbol}d*`), '');
        let anion2Symbol = formula2.replace(new RegExp(`^${cation2Symbol}d*`), '');
        // Buscar objetos de íons
        let cation1 = commonCations.find(c => c.symbol.includes(cation1Symbol)) || { symbol: cation1Symbol, charge: 1 };
        let cation2 = commonCations.find(c => c.symbol.includes(cation2Symbol)) || { symbol: cation2Symbol, charge: 1 };
        let anion1 = commonAnions.find(a => a.symbol.includes(anion1Symbol)) || { symbol: anion1Symbol, charge: -1 };
        let anion2 = commonAnions.find(a => a.symbol.includes(anion2Symbol)) || { symbol: anion2Symbol, charge: -1 };
        // Trocar os íons e montar fórmulas balanceadas
        const product1 = buildIonicFormula(cation1, anion2);
        const product2 = buildIonicFormula(cation2, anion1);
        return [product1, product2];
      }
      return [];
    } catch (error) {
      return [];
    }
  };

  // 5. COMBUSTÃO: Hidrocarboneto/Composto Orgânico + O2 → CO2 + H2O
  const predictCombustionProducts = (compound1, compound2) => {
    try {
      // Função robusta para identificar O2
      const isOxygen = (c) => {
        if (c.class === 'oxygen') return true;
        if (typeof c.formula === 'string') {
          const f = c.formula.replace(/\s/g, '').toUpperCase().replace(/[₂]/g, '2');
          return f === 'O2';
        }
        // Verificar se é elemento O simples
        if (c.type === 'element' && c.symbol === 'O') return true;
        // Verificar se tem apenas oxigênio nos elementos
        if (c.elements && c.elements.length === 1 && c.elements[0].simbolo === 'O') {
          return true;
        }
        return false;
      };

      // Função robusta para identificar hidrocarboneto/composto orgânico
      const isOrganic = (c) => {
        if ([
          'hydrocarbon',
          'organic_compound',
          'organic_oxygenated'
        ].includes(c.class)) return true;
        if (!c.elements) return false;
        const symbols = c.elements.map(e => e.simbolo);
        // Hidrocarboneto: só C e H
        if (symbols.includes('C') && symbols.includes('H') && symbols.every(s => ['C','H'].includes(s))) return true;
        // Orgânico: C, H, O
        if (symbols.includes('C') && symbols.includes('H') && symbols.includes('O') && symbols.every(s => ['C','H','O'].includes(s))) return true;
        return false;
      };

      let organicCompound = null;
      let oxygenCompound = null;

      // Tenta todas as combinações possíveis
      if (isOrganic(compound1) && isOxygen(compound2)) {
        organicCompound = compound1;
        oxygenCompound = compound2;
      } else if (isOrganic(compound2) && isOxygen(compound1)) {
        organicCompound = compound2;
        oxygenCompound = compound1;
      }

      // Fallback: tenta identificar por elementos se não reconheceu
      if (!organicCompound || !oxygenCompound) {
        const tryOrganic = (c) => {
          if (!c.elements) return false;
          const symbols = c.elements.map(e => e.simbolo);
          return symbols.includes('C') && symbols.includes('H');
        };

        const tryOxygen = (c) => {
          if (!c.elements) return false;
          const symbols = c.elements.map(e => e.simbolo);
          return symbols.length === 1 && symbols[0] === 'O';
        };

        if (tryOrganic(compound1) && (isOxygen(compound2) || tryOxygen(compound2))) {
          organicCompound = compound1;
          oxygenCompound = compound2;
        } else if (tryOrganic(compound2) && (isOxygen(compound1) || tryOxygen(compound1))) {
          organicCompound = compound2;
          oxygenCompound = compound1;
        }
      }

      // Se ainda não reconheceu, retorna []
      if (!organicCompound || !oxygenCompound) {
        // DEBUG
        console.log('DEBUG combustao: não reconheceu reagentes como orgânico + O2', {
          compound1: { formula: compound1.formula, class: compound1.class, elements: compound1.elements },
          compound2: { formula: compound2.formula, class: compound2.class, elements: compound2.elements }
        });
        return [];
      }

      // Checa se realmente tem C e H
      const elements = organicCompound.elements || [];
      const hasCarbon = elements.some(el => el.simbolo === 'C');
      const hasHydrogen = elements.some(el => el.simbolo === 'H');

      if (hasCarbon && hasHydrogen) {
        // Combustão completa sempre produz CO2 e H2O
        return ['CO2', 'H2O'];
      } else if (hasCarbon) {
        // Só carbono produz apenas CO2
        return ['CO2'];
      } else if (hasHydrogen) {
        // Só hidrogênio produz apenas H2O
        return ['H2O'];
      }

      return [];
    } catch (error) {
      console.log('DEBUG erro combustao:', error);
      return [];
    }
  };

  // Função principal para prever produtos
  const predictProducts = () => {
    if (!reactant1 || !reactant2) {
      setPredictError('Please enter both reactants');
      setPredictedProducts([]);
      setSelectedReaction(null);
      return;
    }


    try {
      const compound1 = identifyCompoundType(reactant1.trim());
      const compound2 = identifyCompoundType(reactant2.trim());

      let products = [];
      let reactionType = '';


      // 1. COMBUSTÃO: Hidrocarboneto/Composto Orgânico + O2 → CO2 + H2O (prioridade alta)
      const combustionProducts = predictCombustionProducts(compound1, compound2);
      if (combustionProducts.length > 0) {
        products = combustionProducts;
        reactionType = 'Combustion';
      }
      // 2. NEUTRALIZAÇÃO: Ácido + Base → Sal + H2O
      else if ((compound1.class === 'acid' && compound2.class === 'base') ||
          (compound1.class === 'base' && compound2.class === 'acid')) {
        const acid = compound1.class === 'acid' ? compound1 : compound2;
        const base = compound1.class === 'base' ? compound1 : compound2;
        products = predictNeutralizationProducts(acid, base);
        reactionType = 'Neutralization';
      }
      // 3. SÍNTESE: Elemento + Elemento → Composto
      else if (compound1.type === 'element' && compound2.type === 'element') {
        products = predictSynthesisProducts(compound1, compound2);
        reactionType = 'Synthesis';
      }
      // 4. TROCA SIMPLES: Elemento + Composto → Elemento + Composto
      else if (
        (compound1.type === 'element' && compound2.type === 'compound') ||
        (compound2.type === 'element' && compound1.type === 'compound')
      ) {
        // Garante que um é elemento e o outro é composto
        products = predictSingleDisplacementProducts(compound1, compound2);
        reactionType = 'Single Displacement';
      }
      // 5. TROCA DUPLA: Composto + Composto → Composto + Composto
      else if (compound1.type === 'compound' && compound2.type === 'compound') {
        products = predictDoubleDisplacementProducts(compound1, compound2);
        reactionType = 'Double Displacement';
      }
      else {
        setPredictError(
          `Reaction type not recognized ou não suportado. Tipos identificados: '${compound1.type}${compound1.class ? ' ('+compound1.class+')' : ''}' e '${compound2.type}${compound2.class ? ' ('+compound2.class+')' : ''}'.\n`
        );
        setPredictedProducts([]);
        setSelectedReaction(null);
        return;
      }

      if (products.length === 0) {

        setPredictError('Could not predict products for this reaction');
        setPredictedProducts([]);
        setSelectedReaction(null);
        return;
      }

      // Corrigir produtos diatômicos isolados antes de balancear
      const correctedProducts = products.map(p => enforceDiatomic(p));

      // Corrigir reagentes diatômicos isolados também (opcional, mas seguro)
      const reactants = [
        { formula: enforceDiatomic(reactant1.trim()), coefficient: 1 },
        { formula: enforceDiatomic(reactant2.trim()), coefficient: 1 }
      ];
      const productsForBalance = correctedProducts.map(p => ({ formula: p, coefficient: 1 }));

      // DEBUG: mostrar matriz de balanceamento e compostos
      try {
        const allCompounds = [...reactants, ...productsForBalance];
        const allElements = new Set();
        allCompounds.forEach(compound => {
          let parseResult;
          try {
            parseResult = parseComposto(compound.formula);
          } catch (err) {
            console.error('DEBUG erro parseComposto:', compound.formula, err);
            throw new Error(`Erro ao parsear composto: ${compound.formula}`);
          }
          const elements = parseResult.elementos || parseResult;
          if (!Array.isArray(elements) || elements.length === 0) {
            console.error('DEBUG composto malformado:', compound.formula, parseResult);
            throw new Error(`Composto malformado ou vazio: ${compound.formula}`);
          }
          elements.forEach(el => allElements.add(el.simbolo));
        });
        const elementsList = Array.from(allElements);
        const matrix = [];
        elementsList.forEach(element => {
          const row = [];
          allCompounds.forEach((compound, index) => {
            let parseResult;
            // Logar antes de parsear cada composto (incluindo produtos)
            console.log('DEBUG antes do parseComposto (linha):', compound.formula);
            try {
              parseResult = parseComposto(compound.formula);
            } catch (err) {
              console.error('DEBUG erro parseComposto (linha):', compound.formula, err);
              throw new Error(`Erro ao parsear composto (linha): ${compound.formula}`);
            }
            const elements = parseResult.elementos || parseResult;
            if (!Array.isArray(elements) || elements.length === 0) {
              console.error('DEBUG composto malformado (linha):', compound.formula, parseResult);
              throw new Error(`Composto malformado ou vazio (linha): ${compound.formula}`);
            }
            console.log('DEBUG resultado parseComposto (linha):', compound.formula, elements);
            const elementData = elements.find(el => el.simbolo === element);
            const count = elementData ? elementData.quantidade : 0;
            // Produtos negativos
            if (index >= reactants.length) {
              row.push(-count);
            } else {
              row.push(count);
            }
          });
          matrix.push(row);
        });
        console.log('DEBUG balanceamento:', {
          reactants,
          productsForBalance,
          elementsList,
          matrix
        });
        // Log extra para garantir que chegou até aqui
        console.log('DEBUG chegou antes do balanceEquation');
      } catch (e) {
        console.error('DEBUG erro ao montar matriz de balanceamento:', e);
        setPredictError(
          'Erro ao montar matriz de balanceamento:\n' +
          (e && e.message ? e.message : e) +
          (e && e.stack ? '\nStack: ' + e.stack : '')
        );
        // Exibir erro no console para garantir visibilidade
        throw e;
      }

      // Definir balanceResult antes de usar
      const balanceResult = balanceEquation(reactants, productsForBalance);
      console.log('DEBUG depois de chamar balanceEquation', balanceResult);

      // Formatar a equação balanceada
      let balancedEquation = '';
      if (balanceResult.isBalanced) {
        // Função para formatar composto removendo número inicial duplicado
        const formatCompound = (coefficient, formula) => {
          const cleanFormula = formula.replace(/^\d+/, '');
          return (coefficient === 1 ? '' : coefficient) + formatChemicalFormula(cleanFormula);
        };

        const balancedReactants = balanceResult.balancedReactants
          .map(r => formatCompound(r.coefficient, r.formula))
          .join(' + ');

        const balancedProducts = balanceResult.balancedProducts
          .map(p => formatCompound(p.coefficient, p.formula))
          .join(' + ');

        balancedEquation = `${balancedReactants} → ${balancedProducts}`;
      } else {
        let errorMsg = 'Could not balance the equation automatically.';
        if (balanceResult.chargeBalanced === false) {
          errorMsg += ' The equation is not charge-balanced for ionic compounds.';
        }
        errorMsg += ' Please edit the coefficients manually.';
        setPredictError(errorMsg);
        setPredictedProducts([]);
        setSelectedReaction(null);
        return;
      }

      setPredictedProducts(correctedProducts);
      setSelectedReaction({
        type: reactionType,
        equation: balancedEquation,
        isBalanced: balanceResult.isBalanced
      });
      setPredictError('');

    } catch (error) {
      setPredictError(`Error predicting products: ${error.message}`);
      setPredictedProducts([]);
      setSelectedReaction(null);
    }
  };

  return (
    <div className="product-predictor-container">
      <h3>Predict Reaction Products</h3>

      <div className="equation-format" style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: '20px',
        fontSize: '1.2em'
      }}>
        <div style={{
          border: '1px solid var(--border-color)',
          borderRadius: '4px',
          flex: '1',
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          overflow: 'hidden',
          height: '40px'
        }}>
          <input
            type="text"
            value={reactant1}
            onChange={(e) => setReactant1(e.target.value)}
            placeholder="Reactant 1"
            style={{
              position: 'relative',
              top: '0px',
              width: '100%',
              height: '100%',
              padding: '0',
              paddingTop: '0',
              border: 'none',
              outline: 'none',
              fontSize: '1em',
              textAlign: 'center',
              color: 'white',
              backgroundColor: 'transparent',
              lineHeight: '32px'
            }}
          />
        </div>
        <div style={{ fontWeight: 'bold', fontSize: '1.2em', padding: '0 10px' }}>+</div>
        <div style={{
          border: '1px solid var(--border-color)',
          borderRadius: '4px',
          flex: '1',
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          overflow: 'hidden',
          height: '40px'
        }}>
          <input
            type="text"
            value={reactant2}
            onChange={(e) => setReactant2(e.target.value)}
            placeholder="Reactant 2"
            style={{
              position: 'relative',
              top: '0px',
              width: '100%',
              height: '100%',
              padding: '0',
              paddingTop: '0',
              border: 'none',
              outline: 'none',
              fontSize: '1em',
              textAlign: 'center',
              color: 'white',
              backgroundColor: 'transparent',
              lineHeight: '32px'
            }}
          />
        </div>
        <div style={{ fontWeight: 'bold', fontSize: '1.2em', padding: '0 10px' }}>→</div>
        <div style={{
          border: '1px solid var(--border-color)',
          borderRadius: '4px',
          flex: '1',
          backgroundColor: predictedProducts.length > 0 ? 'rgba(76, 175, 80, 0.1)' : 'rgba(0, 0, 0, 0.3)',
          overflow: 'hidden',
          height: '40px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          color: 'white',
          lineHeight: '32px'
        }}>
          {predictedProducts.length > 0 ? (
            <span>{predictedProducts.map(formatChemicalFormula).join(' + ')}</span>
          ) : (
            <span style={{ color: '#999' }}>?</span>
          )}
        </div>
      </div>

      <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '20px' }}>
        <button
          onClick={predictProducts}
          style={{
            padding: '8px 20px',
            backgroundColor: 'var(--primary-color)',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '1em'
          }}
        >
          Predict Products
        </button>
      </div>

      {predictError && (
        <div className="error-message" style={{
          color: 'orange',
          marginBottom: '15px',
          textAlign: 'center',
          padding: '8px',
          backgroundColor: 'rgba(255, 165, 0, 0.1)',
          borderRadius: '4px'
        }}>
          {predictError}
        </div>
      )}

      {predictedProducts.length > 0 && selectedReaction && (
        <div className="prediction-results" style={{
          marginTop: '20px',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          padding: '15px',
          borderRadius: '5px',
          border: '1px solid var(--border-color)'
        }}>
          <div className="complete-equation" style={{
            fontWeight: 'bold',
            fontSize: '1.2em',
            marginBottom: '15px',
            textAlign: 'center'
          }}>
            {selectedReaction && selectedReaction.equation
              ? selectedReaction.equation
                  .replace(/(\d+)?([A-Za-z0-9()]+|[A-Za-z0-9()]+\([A-Za-z0-9]+\)\d*)/g, (match, coef, formula) => {
                      // Separa coeficiente do composto
                      if (!formula) return match;
                      return (coef ? coef : '') + formatChemicalFormula(formula);
                    })
              : ''}
          </div>

          <div className="reaction-info">
            <p><strong>Reaction type:</strong> {selectedReaction.type}</p>
            <p><strong>Products formed:</strong> {predictedProducts.map(formatChemicalFormula).join(', ')}</p>
            <p><strong>Status:</strong> {selectedReaction.isBalanced ? 
              <span style={{color: 'var(--success-color)'}}>Balanced</span> : 
              <span style={{color: 'var(--warning-color)'}}>Not balanced automatically</span>
            }</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Main component
const ChemicalEquations = () => {
  const [activeTab, setActiveTab] = useState('manual');
  const [reactant1, setReactant1] = useState('');
  const [reactant2, setReactant2] = useState('');
  const [product, setProduct] = useState('');
  const [equation, setEquation] = useState('');
  const [parsedEquation, setParsedEquation] = useState({ reactants: [], products: [] });
  const [balanceResult, setBalanceResult] = useState(null);
  const [error, setError] = useState('');
  const [selectedCompound, setSelectedCompound] = useState(null);
  const [compoundMass, setCompoundMass] = useState('');
  const [stoichiometryResults, setStoichiometryResults] = useState({});

  // Units
  const [massUnit, setMassUnit] = useState('g');

  // Mass unit options
  const massUnitOptions = [
    { value: 'g', label: 'Grams (g)' },
    { value: 'kg', label: 'Kilograms (kg)' },
    { value: 'mg', label: 'Milligrams (mg)' }
  ];

  // Update the equation when reactants or products change
  useEffect(() => {
    if (reactant1 || reactant2 || product) {
      const reactantsStr = [reactant1, reactant2].filter(Boolean).join(' + ');
      const newEquation = `${reactantsStr} -> ${product}`;
      setEquation(newEquation);
    } else {
      setEquation('');
    }
  }, [reactant1, reactant2, product]);

  // Parse the equation when it changes
  useEffect(() => {
    if (equation) {
      try {
        const result = parseEquation(equation);
        if (result.error) {
          setError(result.error);
          setParsedEquation({ reactants: [], products: [] });
        } else {
          setError('');
          setParsedEquation(result);
        }
      } catch (err) {
        setError('Error parsing equation: ' + err.message);
        setParsedEquation({ reactants: [], products: [] });
      }
    } else {
      setParsedEquation({ reactants: [], products: [] });
      setError('');
    }
  }, [equation]);

  // Try to balance the equation when the parsed equation changes
  useEffect(() => {
    if (parsedEquation.reactants.length > 0 && parsedEquation.products.length > 0) {
      const result = balanceEquation(parsedEquation.reactants, parsedEquation.products);
      setBalanceResult(result);
    } else {
      setBalanceResult(null);
    }
  }, [parsedEquation]);

  // Function to calculate stoichiometric relationships
  const calculateStoichiometry = useCallback(() => {
    if (!selectedCompound || !compoundMass || !balanceResult || !balanceResult.isBalanced) {
      setStoichiometryResults({});
      return;
    }

    const mass = parseFloat(compoundMass);
    if (isNaN(mass)) {
      setError('Invalid mass');
      return;
    }

    // Find the selected compound
    const allCompounds = [...parsedEquation.reactants, ...parsedEquation.products];
    const selected = allCompounds.find(c => c.formula === selectedCompound);

    if (!selected) {
      setError('Compound not found');
      return;
    }

    // Calculate moles of the selected compound
    const selectedMolarMass = calcularMassaMolar(selected.formula).massaMolar;
    const selectedMols = mass / selectedMolarMass;

    // Calculate masses of all other compounds
    const results = {};

    allCompounds.forEach(compound => {
      if (compound.formula !== selectedCompound) {
        const molarMass = calcularMassaMolar(compound.formula).massaMolar;
        const molRatio = compound.coefficient / selected.coefficient;
        const mols = selectedMols * molRatio;
        const calculatedMass = mols * molarMass;

        results[compound.formula] = {
          molarMass,
          mols,
          mass: calculatedMass
        };
      }
    });

    setStoichiometryResults(results);
  }, [selectedCompound, compoundMass, balanceResult, parsedEquation]);

  // Calculate stoichiometric relationships when a compound and its mass are selected
  useEffect(() => {
    if (selectedCompound && compoundMass && balanceResult && balanceResult.isBalanced) {
      calculateStoichiometry();
    }
  }, [selectedCompound, compoundMass, balanceResult, calculateStoichiometry]);

  // Function to format the balanced equation
  const formatBalancedEquation = () => {
    if (!balanceResult || !balanceResult.isBalanced) return '';

    const reactantsStr = balanceResult.balancedReactants
      .map(r => (r.coefficient === 1 ? formatChemicalFormula(r.formula) : `${r.coefficient}${formatChemicalFormula(r.formula)}`))
      .join(' + ');

    const productsStr = balanceResult.balancedProducts
      .map(p => (p.coefficient === 1 ? formatChemicalFormula(p.formula) : `${p.coefficient}${formatChemicalFormula(p.formula)}`))
      .join(' + ');

    return `${reactantsStr} -> ${productsStr}`;
  };

  return (
    <div className="chemical-equations-container" style={{ width: '100%', maxWidth: '800px', margin: '0 auto' }}>
      <h2>Chemical Equations</h2>

      {/* Internal tabs */}
      <div style={tabStyles.container}>
        <div style={tabStyles.tabList}>
          <button
            style={{
              ...tabStyles.tab,
              ...(activeTab === 'manual' ? tabStyles.activeTab : {})
            }}
            onClick={() => setActiveTab('manual')}
          >
            Manual Equation
          </button>
          <button
            style={{
              ...tabStyles.tab,
              ...(activeTab === 'predict' ? tabStyles.activeTab : {})
            }}
            onClick={() => setActiveTab('predict')}
          >
            Predict Products
          </button>
        </div>

        <div style={tabStyles.tabContent}>
          {activeTab === 'manual' ? (
            <div className="manual-equation-container">
              <div className="equation-input-container" style={{ marginBottom: '20px' }}>
                <h3 style={{ marginBottom: '15px', textAlign: 'center' }}>Chemical Equation</h3>

                <div className="equation-format" style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%',
                  marginBottom: '20px',
                  fontSize: '1.2em'
                }}>
                  <div style={{
                    border: '1px solid var(--border-color)',
                    borderRadius: '4px',
                    flex: '1',
                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    overflow: 'hidden',
                    height: '40px'
                  }}>
                    <input
                      type="text"
                      value={reactant1}
                      onChange={(e) => setReactant1(e.target.value)}
                      placeholder="Reactant 1"
                      style={{
                        position: 'relative',
                        top: '0px',
                        width: '100%',
                        height: '100%',
                        padding: '0',
                        paddingTop: '0',
                        border: 'none',
                        outline: 'none',
                        fontSize: '1em',
                        textAlign: 'center',
                        color: 'white',
                        backgroundColor: 'transparent',
                        lineHeight: '32px'
                      }}
                    />
                  </div>
                  <div style={{ fontWeight: 'bold', fontSize: '1.2em', padding: '0 10px' }}>+</div>
                  <div style={{
                    border: '1px solid var(--border-color)',
                    borderRadius: '4px',
                    flex: '1',
                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    overflow: 'hidden',
                    height: '40px'
                  }}>
                    <input
                      type="text"
                      value={reactant2}
                      onChange={(e) => setReactant2(e.target.value)}
                      placeholder="Reactant 2"
                      style={{
                        position: 'relative',
                        top: '0px',
                        width: '100%',
                        height: '100%',
                        padding: '0',
                        paddingTop: '0',
                        border: 'none',
                        outline: 'none',
                        fontSize: '1em',
                        textAlign: 'center',
                        color: 'white',
                        backgroundColor: 'transparent',
                        lineHeight: '32px'
                      }}
                    />
                  </div>
                  <div style={{ fontWeight: 'bold', fontSize: '1.2em', padding: '0 10px' }}>→</div>
                  <div style={{
                    border: '1px solid var(--border-color)',
                    borderRadius: '4px',
                    flex: '1',
                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    overflow: 'hidden',
                    height: '40px'
                  }}>
                    <input
                      type="text"
                      value={product}
                      onChange={(e) => setProduct(e.target.value)}
                      placeholder="Product"
                      style={{
                        position: 'relative',
                        top: '0px',
                        width: '100%',
                        height: '100%',
                        padding: '0',
                        paddingTop: '0',
                        border: 'none',
                        outline: 'none',
                        fontSize: '1em',
                        textAlign: 'center',
                        color: 'white',
                        backgroundColor: 'transparent',
                        lineHeight: '32px'
                      }}
                    />
                  </div>
                </div>

                <p className="hint" style={{ fontSize: '0.8rem', color: '#888', textAlign: 'center' }}>
                  Example: H₂ + O → H₂O
                </p>
              </div>

              <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '20px' }}>
                <button
                  onClick={() => {
                    if (equation) {
                      try {
                        const result = parseEquation(equation);
                        if (result.error) {
                          setError(result.error);
                        } else {
                          setError('');
                          const balanceResult = balanceEquation(result.reactants, result.products);
                          setBalanceResult(balanceResult);
                        }
                      } catch (err) {
                        setError('Error parsing equation: ' + err.message);
                      }
                    } else {
                      setError('Please enter a chemical equation');
                    }
                  }}
                  style={{
                    padding: '8px 20px',
                    backgroundColor: 'var(--primary-color)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '1em'
                  }}
                >
                  Balance Equation
                </button>
              </div>

              {error && (
                <div className="error-message" style={{
                  color: 'red',
                  marginBottom: '15px',
                  textAlign: 'center',
                  padding: '8px',
                  backgroundColor: 'rgba(255, 0, 0, 0.1)',
                  borderRadius: '4px'
                }}>
                  {error}
                </div>
              )}

              {balanceResult && (
                <div className="balance-result" style={{
                  marginBottom: '20px',
                  backgroundColor: 'rgba(76, 175, 80, 0.1)',
                  padding: '15px',
                  borderRadius: '5px',
                  border: '1px solid var(--border-color)'
                }}>
                  <h3 style={{ textAlign: 'center', marginBottom: '10px' }}>Balancing Result</h3>

                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: '15px'
                  }}>
                    <div style={{
                      padding: '5px 15px',
                      borderRadius: '20px',
                      backgroundColor: balanceResult.isBalanced ? 'rgba(76, 175, 80, 0.2)' : 'rgba(255, 0, 0, 0.2)',
                      color: balanceResult.isBalanced ? 'green' : 'red',
                      fontWeight: 'bold'
                    }}>
                      {balanceResult.isBalanced ? 'Equation Balanced' : 'Equation Not Balanced'}
                    </div>
                  </div>

                  {balanceResult.isBalanced && (
                    <div className="balanced-equation" style={{
                      fontWeight: 'bold',
                      marginTop: '10px',
                      fontSize: '1.2em',
                      textAlign: 'center',
                      padding: '10px',
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '4px',
                      border: '1px dashed var(--border-color)'
                    }}>
                      {formatBalancedEquation()}
                    </div>
                  )}
                </div>
              )}

              {balanceResult && balanceResult.isBalanced && (
                <div className="stoichiometry-container" style={{ marginTop: '30px' }}>
                  <h3>Stoichiometric Calculations</h3>

                  <div className="stoichiometry-inputs" style={{ display: 'flex', gap: '15px', marginBottom: '20px' }}>
                    <div style={{ flex: 2 }}>
                      <label>Select a compound:</label>
                      <select
                        value={selectedCompound || ''}
                        onChange={(e) => setSelectedCompound(e.target.value)}
                        style={{ width: '100%', padding: '8px', marginTop: '5px', backgroundColor: 'rgba(0, 0, 0, 0.3)', color: 'white' }}
                      >
                        <option value="">Select...</option>
                        {[...parsedEquation.reactants, ...parsedEquation.products].map((compound, index) => (
                          <option key={index} value={compound.formula}>
                            {formatChemicalFormula(compound.formula)}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div style={{ flex: 1 }}>
                      <label>Mass:</label>
                      <div style={{ display: 'flex', alignItems: 'center', marginTop: '5px' }}>
                        <input
                          type="number"
                          value={compoundMass}
                          onChange={(e) => setCompoundMass(e.target.value)}
                          style={{ flex: 1, padding: '8px', backgroundColor: 'rgba(0, 0, 0, 0.3)', color: 'white' }}
                        />
                        <div style={{ width: '80px', marginLeft: '5px' }}>
                          <Select
                            value={massUnitOptions.find(opt => opt.value === massUnit)}
                            onChange={(option) => setMassUnit(option.value)}
                            options={massUnitOptions}
                            styles={selectStyles}
                            isSearchable={false}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {Object.keys(stoichiometryResults).length > 0 && (
                    <div className="stoichiometry-results">
                      <h4>Results:</h4>
                      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                        <thead>
                          <tr>
                            <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Compound</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Molar Mass (g/mol)</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Moles (mol)</th>
                            <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Mass (g)</th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.entries(stoichiometryResults).map(([formula, data], index) => (
                            <tr key={index}>
                              <td style={{ border: '1px solid #ddd', padding: '8px' }}>{formatChemicalFormula(formula)}</td>
                              <td style={{ border: '1px solid #ddd', padding: '8px' }}>{formatScientificNotation(data.molarMass)}</td>
                              <td style={{ border: '1px solid #ddd', padding: '8px' }}>{formatScientificNotation(data.mols)}</td>
                              <td style={{ border: '1px solid #ddd', padding: '8px' }}>{formatScientificNotation(data.mass)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <ProductPredictor />
          )}
        </div>
      </div>
    </div>
  );
};

export default ChemicalEquations;

.nomenclature-converter {
  padding: 20px;
  color: white;
}

.converter-header {
  text-align: center;
  margin-bottom: 25px;
}

.converter-header h3 {
  margin: 0 0 10px 0;
  color: #4CAF50;
  font-size: 1.5em;
}

.converter-header p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.search-section {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.input-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.input-type-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-type-selector label {
  font-weight: 600;
  color: #4CAF50;
  min-width: 80px;
}

.type-select {
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: white;
  font-size: 14px;
}

.search-input-group {
  display: flex;
  gap: 10px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 16px;
}

.search-input:focus {
  outline: none;
  border-color: #4CAF50;
}

.search-btn {
  padding: 12px 20px;
  background: linear-gradient(45deg, #4CAF50, #2196F3);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.results-section {
  margin-bottom: 25px;
}

.molecule-result {
  padding: 20px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 8px;
}

.molecule-result h4 {
  margin: 0 0 15px 0;
  color: #4CAF50;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.result-item {
  padding: 10px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.result-item strong {
  color: #66BB6A;
}

.view-3d-btn {
  padding: 10px 20px;
  background: linear-gradient(45deg, #2196F3, #9C27B0);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.view-3d-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.analysis-result {
  padding: 20px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
}

.analysis-result h4 {
  margin: 0 0 15px 0;
  color: #FFC107;
}

.analysis-info p {
  margin: 8px 0;
}

.rules-list {
  margin-top: 10px;
}

.rules-list ul {
  margin: 5px 0;
  padding-left: 20px;
}

.similar-molecules {
  margin-bottom: 25px;
}

.similar-molecules h4 {
  margin: 0 0 15px 0;
  color: #4CAF50;
}

.similar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.similar-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.similar-item:hover {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
  transform: translateY(-2px);
}

.similar-name {
  font-weight: 600;
  margin-bottom: 4px;
  text-transform: capitalize;
}

.similar-formula {
  font-size: 12px;
  opacity: 0.8;
  font-family: 'Courier New', monospace;
  margin-bottom: 2px;
}

.similar-iupac {
  font-size: 11px;
  opacity: 0.7;
  font-style: italic;
}

.functional-groups-reference {
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.functional-groups-reference h4 {
  margin: 0 0 15px 0;
  color: #4CAF50;
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.group-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  text-align: center;
}

.group-name {
  font-weight: 600;
  margin-bottom: 4px;
  color: #66BB6A;
}

.group-suffix {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
}

.group-example {
  font-size: 11px;
  opacity: 0.7;
  font-style: italic;
}

/* Responsividade */
@media (max-width: 768px) {
  .nomenclature-converter {
    padding: 15px;
  }
  
  .input-controls {
    gap: 10px;
  }
  
  .input-type-selector {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input-group {
    flex-direction: column;
  }
  
  .result-grid {
    grid-template-columns: 1fr;
  }
  
  .similar-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .groups-grid {
    grid-template-columns: 1fr;
  }
}

/* Animações */
.results-section,
.similar-molecules,
.functional-groups-reference {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.organic-reactions {
  padding: 20px;
  color: white;
}

.reactions-header {
  text-align: center;
  margin-bottom: 25px;
}

.reactions-header h3 {
  margin: 0 0 10px 0;
  color: #4CAF50;
  font-size: 1.5em;
}

.reactions-header p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.current-molecule-info {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 8px;
  text-align: center;
}

.current-molecule-info h4 {
  margin: 0 0 5px 0;
  color: #4CAF50;
}

.current-molecule-info p {
  margin: 0;
  opacity: 0.8;
  font-size: 14px;
}

.reaction-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.reaction-card {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reaction-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(76, 175, 80, 0.5);
  transform: translateY(-2px);
}

.reaction-card.selected {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.reaction-name {
  font-size: 1.2em;
  font-weight: 600;
  margin-bottom: 10px;
  color: #4CAF50;
}

.reaction-description {
  margin-bottom: 10px;
  opacity: 0.9;
  font-size: 14px;
}

.reaction-example {
  margin-bottom: 10px;
  font-family: 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px;
  border-radius: 4px;
  font-size: 13px;
}

.reaction-conditions {
  font-size: 12px;
  opacity: 0.8;
}

.reaction-conditions strong {
  color: #66BB6A;
}

.reaction-details {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.reaction-details h4 {
  margin: 0 0 20px 0;
  color: #4CAF50;
  font-size: 1.3em;
  text-align: center;
}

.reaction-info {
  display: grid;
  gap: 20px;
}

.info-section,
.mechanisms-section,
.prediction-section {
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-section h5,
.mechanisms-section h5,
.prediction-section h5 {
  margin: 0 0 10px 0;
  color: #66BB6A;
  font-size: 1.1em;
}

.info-section p {
  margin: 8px 0;
  font-size: 14px;
}

.mechanism-card {
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mechanism-card h6 {
  margin: 0 0 10px 0;
  color: #4CAF50;
  font-size: 1em;
}

.mechanism-steps,
.mechanism-characteristics {
  margin-bottom: 10px;
}

.mechanism-steps strong,
.mechanism-characteristics strong {
  color: #66BB6A;
  font-size: 14px;
}

.mechanism-steps ol,
.mechanism-characteristics ul {
  margin: 5px 0;
  padding-left: 20px;
}

.mechanism-steps li,
.mechanism-characteristics li {
  margin: 3px 0;
  font-size: 13px;
}

.prediction-result {
  padding: 10px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.prediction-result p {
  margin: 5px 0;
  font-size: 14px;
}

.prediction-result strong {
  color: #2196F3;
}

.reaction-tips {
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.reaction-tips h4 {
  margin: 0 0 15px 0;
  color: #4CAF50;
  text-align: center;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.tip-card {
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tip-card h5 {
  margin: 0 0 10px 0;
  color: #66BB6A;
  font-size: 1em;
}

.tip-card p {
  margin: 0;
  font-size: 13px;
  opacity: 0.9;
}

/* Responsividade */
@media (max-width: 768px) {
  .organic-reactions {
    padding: 15px;
  }
  
  .reaction-types-grid {
    grid-template-columns: 1fr;
  }
  
  .reaction-info {
    grid-template-columns: 1fr;
  }
  
  .tips-grid {
    grid-template-columns: 1fr;
  }
  
  .reaction-card {
    padding: 15px;
  }
  
  .reaction-details {
    padding: 15px;
  }
}

/* Animações */
.reaction-details {
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects */
.mechanism-card:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(76, 175, 80, 0.3);
}

.tip-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
  transform: translateY(-2px);
}

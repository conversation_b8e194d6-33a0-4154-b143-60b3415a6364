.organic-chemistry-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 12px;
  color: white;
  min-height: 600px;
}

.organic-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.organic-header h2 {
  margin: 0 0 10px 0;
  font-size: 2.2em;
  background: linear-gradient(45deg, #4CAF50, #2196F3, #9C27B0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.organic-header p {
  margin: 0;
  opacity: 0.8;
  font-size: 1.1em;
}

/* Input de molécula */
.molecule-input-section {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.input-group {
  margin-bottom: 15px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #4CAF50;
}

.molecule-input {
  width: 100%;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
}

.molecule-input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.molecule-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.molecule-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.info-item {
  padding: 10px 15px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.info-item strong {
  color: #4CAF50;
}

/* Navegação por abas */
.organic-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 20px;
  background: rgba(0, 0, 0, 0.3);
  padding: 5px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.organic-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 15px 10px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.organic-tab:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.organic-tab.active {
  background: linear-gradient(45deg, #4CAF50, #2196F3);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.tab-icon {
  font-size: 24px;
}

.tab-title {
  font-weight: 600;
  font-size: 12px;
}

/* Conteúdo */
.organic-content {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
  margin-bottom: 30px;
}

/* Moléculas de exemplo */
.example-molecules {
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.example-molecules h3 {
  margin: 0 0 15px 0;
  color: #4CAF50;
  font-size: 1.2em;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
}

.example-molecule {
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.example-molecule:hover {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
  transform: translateY(-2px);
}

.example-name {
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 4px;
  text-transform: capitalize;
}

.example-formula {
  font-size: 11px;
  opacity: 0.8;
  font-family: 'Courier New', monospace;
}

/* Responsividade */
@media (max-width: 768px) {
  .organic-chemistry-container {
    padding: 15px;
  }
  
  .organic-header h2 {
    font-size: 1.8em;
  }
  
  .organic-tabs {
    flex-direction: column;
  }
  
  .organic-tab {
    flex-direction: row;
    justify-content: center;
    padding: 12px;
  }
  
  .tab-icon {
    font-size: 20px;
  }
  
  .tab-title {
    font-size: 14px;
    margin-left: 8px;
  }
  
  .molecule-info {
    grid-template-columns: 1fr;
  }
  
  .example-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.organic-content > * {
  animation: fadeIn 0.5s ease;
}

/* Efeitos de hover */
.organic-chemistry-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    rgba(76, 175, 80, 0.05) 0%, 
    rgba(33, 150, 243, 0.05) 50%, 
    rgba(156, 39, 176, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 12px;
}

.organic-chemistry-container:hover::before {
  opacity: 1;
}

.organic-chemistry-container {
  position: relative;
}

import React, { useEffect, useRef, useState } from 'react';
import './MoleculeViewer3D.css';

const MoleculeViewer3D = ({ composto, elementosInfo }) => {
  const containerRef = useRef(null);
  const [viewer, setViewer] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Função para gerar coordenadas 3D básicas baseadas na fórmula
  const generateMoleculeStructure = (formula, elements) => {
    if (!elements || elements.length === 0) {
      return null;
    }

    // Estruturas conhecidas simples
    const knownStructures = {
      'H2O': {
        atoms: [
          { element: 'O', x: 0, y: 0, z: 0 },
          { element: 'H', x: 0.96, y: 0, z: 0 },
          { element: 'H', x: -0.24, y: 0.93, z: 0 }
        ],
        bonds: [
          { from: 0, to: 1 },
          { from: 0, to: 2 }
        ]
      },
      'CO2': {
        atoms: [
          { element: 'C', x: 0, y: 0, z: 0 },
          { element: 'O', x: -1.16, y: 0, z: 0 },
          { element: 'O', x: 1.16, y: 0, z: 0 }
        ],
        bonds: [
          { from: 0, to: 1, order: 2 },
          { from: 0, to: 2, order: 2 }
        ]
      },
      'CH4': {
        atoms: [
          { element: 'C', x: 0, y: 0, z: 0 },
          { element: 'H', x: 1.09, y: 0, z: 0 },
          { element: 'H', x: -0.36, y: 1.03, z: 0 },
          { element: 'H', x: -0.36, y: -0.51, z: 0.89 },
          { element: 'H', x: -0.36, y: -0.51, z: -0.89 }
        ],
        bonds: [
          { from: 0, to: 1 },
          { from: 0, to: 2 },
          { from: 0, to: 3 },
          { from: 0, to: 4 }
        ]
      },
      'NH3': {
        atoms: [
          { element: 'N', x: 0, y: 0, z: 0 },
          { element: 'H', x: 0.94, y: 0, z: 0 },
          { element: 'H', x: -0.47, y: 0.81, z: 0 },
          { element: 'H', x: -0.47, y: -0.41, z: 0.71 }
        ],
        bonds: [
          { from: 0, to: 1 },
          { from: 0, to: 2 },
          { from: 0, to: 3 }
        ]
      },
      'HCl': {
        atoms: [
          { element: 'H', x: 0, y: 0, z: 0 },
          { element: 'Cl', x: 1.27, y: 0, z: 0 }
        ],
        bonds: [
          { from: 0, to: 1 }
        ]
      },
      'C2H6': {
        atoms: [
          { element: 'C', x: -0.77, y: 0, z: 0 },
          { element: 'C', x: 0.77, y: 0, z: 0 },
          { element: 'H', x: -1.16, y: 1.03, z: 0 },
          { element: 'H', x: -1.16, y: -0.51, z: 0.89 },
          { element: 'H', x: -1.16, y: -0.51, z: -0.89 },
          { element: 'H', x: 1.16, y: 1.03, z: 0 },
          { element: 'H', x: 1.16, y: -0.51, z: 0.89 },
          { element: 'H', x: 1.16, y: -0.51, z: -0.89 }
        ],
        bonds: [
          { from: 0, to: 1 },
          { from: 0, to: 2 }, { from: 0, to: 3 }, { from: 0, to: 4 },
          { from: 1, to: 5 }, { from: 1, to: 6 }, { from: 1, to: 7 }
        ]
      },
      'C2H4': {
        atoms: [
          { element: 'C', x: -0.67, y: 0, z: 0 },
          { element: 'C', x: 0.67, y: 0, z: 0 },
          { element: 'H', x: -1.23, y: 0.93, z: 0 },
          { element: 'H', x: -1.23, y: -0.93, z: 0 },
          { element: 'H', x: 1.23, y: 0.93, z: 0 },
          { element: 'H', x: 1.23, y: -0.93, z: 0 }
        ],
        bonds: [
          { from: 0, to: 1, order: 2 },
          { from: 0, to: 2 }, { from: 0, to: 3 },
          { from: 1, to: 4 }, { from: 1, to: 5 }
        ]
      },
      'C2H5OH': {
        atoms: [
          { element: 'C', x: -1.31, y: -0.25, z: 0 },
          { element: 'C', x: 0, y: 0.5, z: 0 },
          { element: 'O', x: 1.31, y: -0.25, z: 0 },
          { element: 'H', x: -1.31, y: -1.34, z: 0 },
          { element: 'H', x: -2.24, y: 0.25, z: 0 },
          { element: 'H', x: -1.31, y: -0.25, z: 1.09 },
          { element: 'H', x: 0, y: 1.59, z: 0 },
          { element: 'H', x: 0, y: 0.5, z: 1.09 },
          { element: 'H', x: 2.06, y: 0.25, z: 0 }
        ],
        bonds: [
          { from: 0, to: 1 }, { from: 1, to: 2 },
          { from: 0, to: 3 }, { from: 0, to: 4 }, { from: 0, to: 5 },
          { from: 1, to: 6 }, { from: 1, to: 7 }, { from: 2, to: 8 }
        ]
      }
    };

    // Verificar se temos uma estrutura conhecida
    if (knownStructures[formula]) {
      return knownStructures[formula];
    }

    // Tentar identificar por padrões comuns
    const elementSymbols = elements.map(el => el.simbolo);
    const elementCounts = {};
    elements.forEach(el => {
      elementCounts[el.simbolo] = el.quantidade;
    });

    // Identificar tipos de moléculas comuns
    if (elementSymbols.includes('C') && elementSymbols.includes('H')) {
      // Hidrocarbonetos
      const carbonCount = elementCounts['C'] || 0;
      const hydrogenCount = elementCounts['H'] || 0;

      // Alcanos: CnH2n+2
      if (hydrogenCount === 2 * carbonCount + 2) {
        if (carbonCount === 1) return knownStructures['CH4'];
        if (carbonCount === 2) return knownStructures['C2H6'];
      }

      // Alcenos: CnH2n
      if (hydrogenCount === 2 * carbonCount) {
        if (carbonCount === 2) return knownStructures['C2H4'];
      }

      // Álcoois: CnH2n+2O
      if (elementSymbols.includes('O') && elementCounts['O'] === 1) {
        if (carbonCount === 2 && hydrogenCount === 6) return knownStructures['C2H5OH'];
      }
    }

    // Compostos inorgânicos simples
    if (elementSymbols.includes('H') && elementSymbols.includes('Cl') && elementSymbols.length === 2) {
      return knownStructures['HCl'];
    }

    if (elementSymbols.includes('H') && elementSymbols.includes('O') && elementSymbols.length === 2) {
      return knownStructures['H2O'];
    }

    // Gerar estrutura simples baseada nos elementos
    const atoms = [];
    const bonds = [];
    let atomIndex = 0;

    // Adicionar átomos baseados na composição
    elements.forEach((element, elementIndex) => {
      for (let i = 0; i < element.quantidade; i++) {
        const angle = (atomIndex * 2 * Math.PI) / elements.reduce((sum, el) => sum + el.quantidade, 0);
        const radius = elementIndex === 0 ? 0 : 1.5; // Primeiro elemento no centro

        atoms.push({
          element: element.simbolo,
          x: radius * Math.cos(angle),
          y: radius * Math.sin(angle),
          z: 0
        });

        // Criar ligações simples (primeiro átomo como centro)
        if (atomIndex > 0) {
          bonds.push({ from: 0, to: atomIndex });
        }

        atomIndex++;
      }
    });

    return { atoms, bonds };
  };



  useEffect(() => {
    // Suprimir erros do ResizeObserver
    const originalError = console.error;
    console.error = (...args) => {
      if (args[0]?.includes?.('ResizeObserver loop completed') ||
          args[0]?.includes?.('ResizeObserver loop limit exceeded')) {
        return;
      }
      originalError.apply(console, args);
    };

    // Função para converter estrutura para formato XYZ (movida para dentro do useEffect)
    const structureToXYZ = (structure) => {
      if (!structure || !structure.atoms) return '';

      let xyz = `${structure.atoms.length}\n`;
      xyz += `Generated structure for ${composto}\n`;

      structure.atoms.forEach(atom => {
        xyz += `${atom.element} ${atom.x.toFixed(6)} ${atom.y.toFixed(6)} ${atom.z.toFixed(6)}\n`;
      });

      return xyz;
    };

    const initViewer = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Importar 3Dmol dinamicamente
        const $3Dmol = await import('3dmol');

        if (containerRef.current) {
          // Limpar container anterior
          containerRef.current.innerHTML = '';

          // Criar novo viewer
          const config = {
            backgroundColor: 'black',
            antialias: true,
            alpha: true
          };

          const newViewer = $3Dmol.createViewer(containerRef.current, config);

          // Gerar estrutura da molécula
          const structure = generateMoleculeStructure(composto, elementosInfo);

          if (structure) {
            // Converter para formato XYZ
            const xyzData = structureToXYZ(structure);

            // Adicionar molécula ao viewer
            newViewer.addModel(xyzData, 'xyz');

            // Configurar estilo
            newViewer.setStyle({}, {
              stick: { radius: 0.1 },
              sphere: { scale: 0.3 }
            });

            // Adicionar labels aos átomos
            const atoms = newViewer.getModel().selectedAtoms({});
            atoms.forEach((atom, index) => {
              newViewer.addLabel(atom.elem, {
                position: atom,
                backgroundColor: 'rgba(0,0,0,0.8)',
                fontColor: 'white',
                fontSize: 12,
                showBackground: true
              });
            });

            // Centralizar e renderizar
            newViewer.zoomTo();
            newViewer.render();

            setViewer(newViewer);
          } else {
            setError('Unable to generate 3D structure for this compound');
          }
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error initializing 3D viewer:', err);
        setError('Failed to load 3D viewer');
        setIsLoading(false);
      }
    };

    if (composto && elementosInfo) {
      initViewer();
    }

    // Cleanup
    return () => {
      // Restaurar console.error original
      console.error = originalError;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [composto, elementosInfo]);

  // Funções de controle
  const resetView = () => {
    if (viewer) {
      viewer.zoomTo();
      viewer.render();
    }
  };

  const toggleStyle = () => {
    if (viewer) {
      // Alternar entre diferentes estilos de visualização
      const currentStyle = viewer.getModel().getStyle();
      if (currentStyle.stick) {
        viewer.setStyle({}, { sphere: { scale: 0.5 } });
      } else {
        viewer.setStyle({}, {
          stick: { radius: 0.1 },
          sphere: { scale: 0.3 }
        });
      }
      viewer.render();
    }
  };

  if (!composto || !elementosInfo) {
    return (
      <div className="molecule-viewer-container">
        <div className="no-data">
          <p>No compound data available for 3D visualization</p>
        </div>
      </div>
    );
  }

  return (
    <div className="molecule-viewer-container">
      <div className="viewer-header">
        <h4>3D Molecular Structure</h4>
        <div className="viewer-controls">
          <button onClick={resetView} className="control-button" title="Reset View">
            🔄
          </button>
          <button onClick={toggleStyle} className="control-button" title="Toggle Style">
            🎨
          </button>
        </div>
      </div>
      
      {isLoading && (
        <div className="loading-indicator">
          <div className="spinner"></div>
          <p>Loading 3D structure...</p>
        </div>
      )}
      
      {error && (
        <div className="error-message">
          <p>{error}</p>
          <p className="error-details">
            Showing simplified structure for: <strong>{composto}</strong>
          </p>
        </div>
      )}
      
      <div 
        ref={containerRef} 
        className="molecule-viewer"
        style={{ 
          width: '100%', 
          height: '300px',
          display: isLoading || error ? 'none' : 'block'
        }}
      />
      
      <div className="viewer-info">
        <p>Formula: <strong>{composto}</strong></p>
        <p className="interaction-hint">
          💡 Click and drag to rotate • Scroll to zoom • Right-click and drag to pan
        </p>
      </div>
    </div>
  );
};

export default MoleculeViewer3D;
